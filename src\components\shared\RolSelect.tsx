'use client';

import {
  typeRoleADVERTISER,
  typeRoleNOTHING,
  typeRoleUSER,
} from '@/models/user';
import { TeamOutlined } from '@ant-design/icons';
import { useTranslations } from 'next-intl';
import React, { useState } from 'react';

import FieldSelectTab from '@/components/commons/FieldSelectTab';

import RegisterForm from '../forms/auth/Register';

const RolSelect = () => {
  const tInput = useTranslations('input');
  const tAuth = useTranslations('auth');
  const [selected, setSelected] = useState<number>(typeRoleNOTHING);

  return (
    <>
      {selected === typeRoleNOTHING ? (
        <div className="base-form w-full animate-fade">
          <h2 className="whitespace-pre text-center text-2xl font-semibold leading-none tracking-tight text-gray-900 mb-3 dark:text-white">
            {tAuth('registerSubtitle')}
          </h2>
          <div className="flex w-full flex-col items-center justify-center gap-6 lg:flex-row lg:gap-8">
            <FieldSelectTab
              onClick={() => setSelected(typeRoleUSER)}
              withDefaultIcon
              name="consumer"
              label={tInput('user')}
            >
              <span className="text-center text-xl font-medium">
                {tAuth('registerTitleUser')}
              </span>
              <span className="text-center text-xs font-medium">
                {tAuth('registerSubtitleUser')}
              </span>
            </FieldSelectTab>
            <FieldSelectTab
              onClick={() => setSelected(typeRoleADVERTISER)}
              name="advertiser"
              label={tInput('advertiser')}
            >
              <TeamOutlined className="text-xl" />
              <span className="text-center text-xl font-medium">
                {tAuth('registerTitleAdvertiser')}
              </span>
              <span className="text-center text-xs font-medium">
                {tAuth('registerSubtitleAdvertiser')}
              </span>
            </FieldSelectTab>
          </div>
        </div>
      ) : (
        <RegisterForm roleSelected={selected} />
      )}
    </>
  );
};

export default RolSelect;
